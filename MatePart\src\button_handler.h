/**************************************************************************************************/
/**
 * @file      : button_handler.h
 * @brief     : Button handling and GPIO interrupt management module
 * @version   : V1.0.0
 * @date      : July-2025
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#ifndef BUTTON_HANDLER_H
#define BUTTON_HANDLER_H

#include <stdint.h>
#include "Z20K11xM_gpio.h"

/* Button state enumeration */
typedef enum {
    BUTTON_NONE = 0,
    BUTTON_1_PRESSED = 1,    
    BUTTON_2_PRESSED = 2,     
    BUTTON_3_PRESSED = 3     
} ButtonState_t;

/* Function declarations */
void Button_Handler_Init(void);
void GPIOIntInit(void);
void PortCInt(PORT_ID_t portId, PORT_GPIONO_t gpioNo);
void Process_Button_CAN(void);
ButtonState_t Get_Button_State(void);
void Clear_Button_State(void);

#endif /* BUTTON_HANDLER_H */
