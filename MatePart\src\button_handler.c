/**************************************************************************************************/
/**
 * @file      : button_handler.c
 * @brief     : Button handling and GPIO interrupt management module
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "button_handler.h"
#include "display_control.h"
#include "can.h"
#include "common_func.h"
#include "Z20K11xM_drv.h"
#include "Z20K11xM_gpio.h"

/* Button state variable */
static volatile ButtonState_t button_pressed = BUTTON_NONE;

/**
 * @brief Initialize button handler module
 */
void Button_Handler_Init(void)
{
    button_pressed = BUTTON_NONE;
}

/**
 * @brief Initialize GPIO interrupts for buttons
 */
void GPIOIntInit(void)
{
    PORT_InstallCallBackFunc(PortCInt);

    /* Clear interrupt flag*/
    PORT_ClearPinInt(PORT_D, GPIO_2);
    /* set pin as GPIO*/
    PORT_PinmuxConfig(PORT_D, GPIO_2, PTD2_GPIO);
    /* an initial voltage */
    PORT_PullConfig(PORT_D, GPIO_2, PORT_PULL_DOWN);
    /* input direction for PTD2 */
    GPIO_SetPinDir(PORT_D, GPIO_2, GPIO_INPUT);
    /* port interrupt config*/
    PORT_PinIntConfig(PORT_D, GPIO_2, PORT_ISF_INT_RISING_EDGE);

    /* Clear interrupt flag for PTC2 - Button 2 */
    PORT_ClearPinInt(PORT_C, GPIO_2);
    /* set pin as GPIO*/
    PORT_PinmuxConfig(PORT_C, GPIO_2, PTC2_GPIO);
    /* an initial voltage */
    PORT_PullConfig(PORT_C, GPIO_2, PORT_PULL_DOWN);
    /* input direction for PTC2 */
    GPIO_SetPinDir(PORT_C, GPIO_2, GPIO_INPUT);
    /* port interrupt config*/
    PORT_PinIntConfig(PORT_C, GPIO_2, PORT_ISF_INT_RISING_EDGE);

    /* Clear interrupt flag for PTC3 - Button 3 */
    PORT_ClearPinInt(PORT_C, GPIO_3);
    /* set pin as GPIO*/
    PORT_PinmuxConfig(PORT_C, GPIO_3, PTC3_GPIO);
    /* an initial voltage */
    PORT_PullConfig(PORT_C, GPIO_3, PORT_PULL_DOWN);
    /* input direction for PTC3 */
    GPIO_SetPinDir(PORT_C, GPIO_3, GPIO_INPUT);
    /* port interrupt config*/
    PORT_PinIntConfig(PORT_C, GPIO_3, PORT_ISF_INT_RISING_EDGE);

    NVIC_SetPriority(PORTABC_IRQn, 1u);
    NVIC_SetPriority(PORTDE_IRQn, 1u);
    /* enable NVIC IRQ*/
    NVIC_EnableIRQ(PORTABC_IRQn);
    NVIC_EnableIRQ(PORTDE_IRQn);
}

/**
 * @brief GPIO interrupt callback function
 * @param portId: Port ID
 * @param gpioNo: GPIO number
 */
void PortCInt(PORT_ID_t portId, PORT_GPIONO_t gpioNo)
{
    switch (portId)
    {
    case PORT_C:
        if (gpioNo == GPIO_2)
        {
            button_pressed = BUTTON_2_PRESSED;  
        }
        else if(gpioNo == GPIO_3)
        {
           button_pressed = BUTTON_3_PRESSED;
        }
        break;
    case PORT_D:
        if (gpioNo == GPIO_2)
        {
            button_pressed = BUTTON_1_PRESSED;  
        }
        break;
    default:
        break;
    }
}

/**
 * @brief Process button press and send CAN data
 */
void Process_Button_CAN(void)
{
    uint8_t can_data[64] = {0};

    if (button_pressed == BUTTON_1_PRESSED)
    {
        /* Button1: Switch to next mode */
        Next_Mode();

        /* Build and send CAN data */
        Build_CAN_Data(can_data);
        CAN_Send_Button1_Msg(0x5F0, can_data);
    }
    else if (button_pressed == BUTTON_2_PRESSED)
    {
        /* Button2: Increase current mode value */
        DisplayMode_t current_mode = Get_Current_Mode();
        switch(current_mode) {
            case MODE_INTERFACE:
                Display_Inc_Interface_Mode();
                break;
            case MODE_GEAR:
                Display_Inc_Gear_Position();
                break;
            case MODE_SPEED:
                Display_Inc_Vehicle_Speed();
                break;
            case MODE_DOOR:
                Display_Inc_Door_Status();
                break;
            case MODE_DRIVE:
                Display_Inc_Drive_Mode();
                break;
        }

        Build_CAN_Data(can_data);
        CAN_Send_Button2_Msg(0x5F0, can_data);
    }
    else if (button_pressed == BUTTON_3_PRESSED)
    {
        /* Button3: Decrease current mode value */
        DisplayMode_t current_mode = Get_Current_Mode();
        switch(current_mode) {
            case MODE_INTERFACE:
                Display_Dec_Interface_Mode();
                break;
            case MODE_GEAR:
                Display_Dec_Gear_Position();
                break;
            case MODE_SPEED:
                Display_Dec_Vehicle_Speed();
                break;
            case MODE_DOOR:
                Display_Dec_Door_Status();
                break;
            case MODE_DRIVE:
                Display_Dec_Drive_Mode();
                break;
        }

        /* Build and send CAN data */
        Build_CAN_Data(can_data);
        CAN_Send_Button3_Msg(0x5F0, can_data);
    }

    delay(100000);
    button_pressed = BUTTON_NONE;
}

/**
 * @brief Get current button state
 * @return Current button state
 */
ButtonState_t Get_Button_State(void)
{
    return button_pressed;
}

/**
 * @brief Clear button state
 */
void Clear_Button_State(void)
{
    button_pressed = BUTTON_NONE;
}
